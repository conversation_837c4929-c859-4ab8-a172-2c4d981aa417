--颂摩崖
--但是可能是火烧，刺杀 job
songmoya = {
	new = function()
		local hw = {}
		setmetatable(hw, { __index = helloworld })
		return hw
	end,
	open_ask = function()
		open_triggers("songmoya_ask", 1, 6)
	end,
	close_ask = function()
		close_triggers("songmoya_ask", 1, 6)
	end,
	open_job = function()

	end,
	close_job = function()

	end,
	open_finish = function()

	end,
	close_finish = function()

	end,
}
---------------


---------------

add_trigger("songmoya_1", "^[ > ]*你向鲁有脚打听有关『报效国家』的消息。", function(params)
	del_timer("input")
	close_trigger("songmoya_1")
	songmoya:open_ask()
end)

--问任务----------
add_trigger("songmoya_ask_1", "^[ > ]*鲁有脚说道：「(?:我这里现在没有什么任务，你等会再来吧。|您上次任务辛苦了，还是先休息一下再说吧。|蒙古大汗暂时没找到踪迹，等会再来吧。|这个任务我已经交给)",
	function(params) --busy
		songmoya:close_ask()
		var["fangqi_job"] = "smy"
		if var["exp"] and var["exp"] > 1000000 and var["time_mark_smy"] == nil then
			var["fangqi_job"] = nil
			if string.find(line[1], "这个任务我已经交给") then
				logs("战宋摩崖:" .. line[1])
			else
				do_log("smy_busy")
			end
			var["time_mark_smy"] = os.time() - 300 --总等待 10 分钟，如果问了一下那么等待5分钟
		end
		b(function()
			exec('changejob')
		end)
	end)
add_trigger("songmoya_ask_2", "^[ > ]*鲁有脚说道：「(?:你不是已经接过任务了吗？|你上次的任务还没有完成，我怎么放心再让你办事。|我这里现在没有什么任务可以给你。)",
	function(params) --quit
		songmoya:close_ask()
		var["fangqi_job"] = "smy"

		if var["time_mark_smy"] == nil then
			if var["exp"] and var["exp"] > 1000000 then -- smy
				var["fangqi_job"] = nil
				if string.find(line[1], "这个任务我已经交给") then
					logs("战宋摩崖:" .. line[1])
				else
					do_log("smy_busy")
				end
				var["time_mark_smy"] = os.time() - 300 --总等待 10 分钟，如果问了一下那么等待5分钟
			elseif var["exp"] and var["exp"] > 500000 then --刺杀
				var["time_mark_smy"] = os.time() - 480 --由于prepare.lua 设置 10分钟那个smy busy，偷懒这里改成时间 -8分钟
			else
				var["time_mark_smy"] = os.time() - 480 --由于prepare.lua 设置 10分钟那个smy busy，偷懒这里改成时间 -8分钟
			end
		end
		b(function()
			exec('changejob')
		end)
	end)
add_trigger("songmoya_ask_3", "^[ > ]*鲁有脚说道：「刚才我接到本帮弟子易大彪飞鸽传书，西夏国主起大军十万，欲侵我大宋。", function(params) --文书
	songmoya:close_ask()
	b(function()
		songmoya:wenshu()
	end)
end)
add_trigger("songmoya_ask_4", "^[ > ]*鲁有脚说道：「蒙古大军侵我大宋襄阳城，每次都以南阳为囤粮之所。", function(params) --火烧
	songmoya:close_ask()
	b(function()
		songmoya:huoshao()
	end)
end)
add_trigger("songmoya_ask_5", "^[ > ]*鲁有脚说道：「蒙古大汗蒙哥令大将粘而帖率精兵数十万，兵分两路，企图犯我襄阳。", function(params) --刺杀
	songmoya:close_ask()
	b(function()
		songmoya:cisha()
	end)
end)
add_trigger("songmoya_ask_6", "^[ > ]*鲁有脚说道：「颂摩崖是西夏武士东来的必经之地，你速带几名弟子埋伏在那里截杀。", function(params) --smy	
	songmoya:close_ask()
	b(function()
		do_log("smy_start")
		songmoya:smy()
	end)
end)
close_trigger("songmoya_1")
songmoya:close_ask()

------------------
function songmoya:huoshao()  --火烧
	exec('set_job_cisha')
	var["flag_job"] = "huoshao" --火烧
	add_alias("job_win", function(params)
		add_trigger("songmoya_8", "^[ > ]*好，任务完成了，你得到了(\\S+)点实战经验，(\\S+)点潜能", function(params)
			var["log_pot"] = params[2]
			var["log_exp"] = params[1]
			do_log("job_reward")
			unset_timer("timer")
			unset_timer("alarm")
			del_trigger("songmoya_8")
			check_busy(function()
				exec('changejob')
			end)
		end)
		g(3153, function()
			set_timer("timer", 2, function()
				send("s")
				send("n")
			end)
			alarm("alarm", 60, function()
				unset_timer("timer")
				exec("changejob")
			end)
		end)
	end)
	add_trigger("songmoya_3", "^[ > ]*元兵说道：「大胆！！！」", function(params)
		set_timer("timer", 60, function(params)
			exec("do_job_huoshao")
		end)
		var["killer_name"] = "元兵"
		var["killer_id"] = "yuan bing"
		var["pfm_id"] = "yuan bing"
		local first_pfm = get_first_pfm(var["first_pfm_smy"])
		exec("kill @pfm_id;kill @pfm_id 2;" .. first_pfm)
		set_fight("cisha")
	end)
	add_trigger("songmoya_4", "^[ > ]*元兵神志迷糊，脚下一个不稳，倒在地上昏了过去。", function(params)
		send("kill yuan bing")
	end)
	add_trigger("songmoya_5", "^[ > ]*元兵「啪」的一声倒在地上，挣扎着抽动了几下就死了。", function(params)
		exes("lead yuan bing", 10)
	end)
	add_trigger("songmoya_6", "^[ > ]*这里没有 yuan bing。", function(params)
		del_timer("input")
		var["do_stop"] = 0
		set_dazuo("huoshao")
		exec("yun jing;yun jingli;yun qi;check_poison check_heal do_job_huoshao")
	end)

	add_trigger("songmoya_7", "^[ > ]*好，任务已经完成，可以回去复命了。", function(params)
		var["do_stop"] = 0
		unset_timer("check_busy_1")
		unset_timer("timer")
		del_timer("input")
		delete_triggers("songmoya", 3, 8)
		check_busy(function()
			exec("w;w;e;n;n;nu;nu;down;job_win")
		end)
	end)

	add_alias("do_huoshao", function(params)
		g(236, function()
			var["killer_name"] = "元兵"
			var["killer_id"] = "yuan bing"
			var["pfm_id"] = "yuan bing"
			set_fight("cisha")
			exec("wield_weapon;yun_powerup do_job_huoshao")
		end)
	end)
	add_alias("do_job_huoshao", function(params)
		function after_gps()
			exec("do_job_huoshao")
		end

		check_busy(function()
			wait1(25, function()
				exec(
					"pa ya;sd;sd;dian dui;s;dian dui;w;dian dui;e;e;dian dui;w;s;dian dui;n;dian dui;w;dian dui;e;e;dian dui;w;n;dian dui;nu;nu")
			end)
		end)
		set_timer("timer", 10, function()
			exec(
				"pa ya;sd;sd;dian dui;s;dian dui;w;dian dui;e;e;dian dui;w;s;dian dui;n;dian dui;w;dian dui;e;e;dian dui;w;n;dian dui;nu;nu")
		end)
	end)
	exec("do_huoshao")
end

--------------------
function songmoya:cisha()  --刺杀
	exec('set_job_cisha')
	var["flag_job"] = "cisha" --cisha

	add_trigger("songmoya_2", "^[ > ]*易大彪带着你混过了蒙古大军的几道岗哨，来到中军营前。", function(params)
		unset_timer("timer")
		del_timer("wait")
	end)
	add_trigger("songmoya_3", "^[ > ]*中军侍卫说道：「大胆！！！」", function(params)
		var["killer_name"] = "中军侍卫"
		var["killer_id"] = "zhongjun shiwei"
		var["pfm_id"] = "zhongjun shiwei"
		local first_pfm = get_first_pfm(var["first_pfm_smy"])
		exec("kill @pfm_id;kill @pfm_id 2;" .. first_pfm)
		set_fight("cisha")
	end)
	add_trigger("songmoya_4", "^[ > ]*中军侍卫神志迷糊，脚下一个不稳，倒在地上昏了过去。", function(params)
		send("kill zhongjun shiwei")
	end)
	add_trigger("songmoya_5", "^[ > ]*中军侍卫「啪」的一声倒在地上，挣扎着抽动了几下就死了。", function(params)
		exes("kill zhongjun shiwei;lead zhongjun shiwei", 10)
	end)
	add_trigger("songmoya_6", "^[ > ]*这里没有 zhongjun shiwei。", function(params)
		var["do_stop"] = 0
		del_timer("input")
		if var["roomname"] and var.roomname == "中军大帐" then
			var["killer_name"] = "粘而帖"
			var["killer_id"] = "zhan ertie"
			var["pfm_id"] = "zhan ertie"
			local first_pfm = get_first_pfm(var["first_pfm_smy"])
			exec("kill @pfm_id;" .. first_pfm)
		else
			set_dazuo("cisha")
			exec("yun jing;yun jingli;yun qi;check_poison check_heal do_job_cisha")
		end
	end)
	add_trigger("songmoya_7", "^[ > ]*好，任务完成了，你得到了(\\S+)点实战经验，(\\S+)点潜能", function(params)
		var["log_pot"] = params[2]
		var["log_exp"] = params[1]
		do_log("job_reward")
	end)
	add_trigger("songmoya_8", "^[ > ]*你趁着混乱冲出了元军大营。", function(params)
		delete_triggers("songmoya", 2, 9)
		var["do_stop"] = 0
		check_busy(function()
			exec('changejob')
		end)
	end)



	add_alias("do_cs", function(params)
		del_timer("wait")
		g(347, function()
			var["killer_name"] = "中军侍卫"
			var["killer_id"] = "zhongjun shiwei"
			var["pfm_id"] = "zhongjun shiwei"
			wait(10, function()
				exec("s;n")
				wait(10, function()
					exec("do_cs")
				end)
			end)
		end)
	end)
	add_alias("do_job_cisha", function(params)
		if var["roomname"] and var.roomname == "中军大帐" then
			var["killer_name"] = "粘而帖"
			var["killer_id"] = "zhan ertie"
			var["pfm_id"] = "zhan ertie"
			exec("get silver from corpse;get gold from corpse;get gold")
			local first_pfm = get_first_pfm(var["first_pfm_smy"])
			exec("kill @pfm_id;" .. first_pfm .. ";kill zhongjun shiwei;kill zhongjun shiwei 2")
			set_fight("cisha")
		else
			exec("get silver from corpse;get gold from corpse;n")
			var["killer_name"] = "中军侍卫"
			var["killer_id"] = "zhongjun shiwei"
			var["pfm_id"] = "zhongjun shiwei"
			local first_pfm = get_first_pfm(var["first_pfm_smy"])
			exec("kill @pfm_id;kill @pfm_id 2;" .. first_pfm)
			--		set_fight("cisha")
		end
	end)
	add_alias("prepare_cisha", function(params)
		exec("do_job_cisha")
	end)
	exec("wield_weapon;yun_powerup do_cs")
end

add_alias("set_job_cisha", function(params)
	var["job_room"] = nil
	var["job_zone"] = nil
	var["killer_name"] = nil
	var["killer_id"] = nil
	var["pfm_id"] = nil
	var["job_start"] = nil --没看到蒙面人
	var["room_list"] = nil
	var["search_list"] = nil
	var["flag_job"] = "smy" --cisha
	close_fight()        --关闭战斗触发
	var["fight"] = 0     --不在战斗状态
	var["idle"] = 0      --没发呆

	send("alias bei_skills " .. var["skills_bei2"])

	add_alias("kill_job_npc", function(params)
		var["pfm_id"] = var["pfm_id"] or "shiwei"
		local first_pfm = get_first_pfm(var["first_pfm_smy"])
		exec("kill @pfm_id;" .. first_pfm)
	end)


	add_alias("job_escape", function(params) --逃跑
		--		exec("do_quit killer")--不逃跑了等死
	end)

	add_alias("job_ask", function(params) --ask
		g(3153, function()
			set_wield_weapon("smy")
			open_trigger("songmoya_1")
			send("cond")
			send("ask lu youjiao about 报效国家")
		end)
	end)

	add_alias("job_fail", function(params)
		--		exec("do_quit quit")
	end)

	add_alias("job_win", function(params)
		var["no_need_heal_qi"] = nil --可以疗伤
		var["do_stop"] = 0
		exec('changejob')
	end)

	add_alias("after_faint", function(params) -- 【after_faint】：设置杀晕倒以后干嘛
		exec("job_fail")
	end)

	add_alias("after_faint2", function(params) -- 【after_faint2】：设置杀晕倒以后干嘛
		exec("job_fail")
	end)
end)

--**************************--
--        宋摩崖            --
--**************************--
add_alias("set_job_smy", function(params)
	var["job_room"] = nil
	var["job_zone"] = nil
	var["killer_name"] = nil
	var["killer_id"] = nil
	var["pfm_id"] = nil
	var["job_start"] = nil --没看到蒙面人
	var["room_list"] = nil
	var["search_list"] = nil
	var["flag_job"] = "smy" --cisha
	close_fight()        --关闭战斗触发
	var["fight"] = 0     --不在战斗状态
	var["idle"] = 0      --没发呆

	send("alias bei_skills " .. var["skills_bei_smy"])

	add_alias("kill_job_npc", function(params)
		var["pfm_id"] = "xixia wushi"
		local first_pfm = get_first_pfm(var["first_pfm_smy"])
		exec("kill @pfm_id;kill @pfm_id 2;" .. first_pfm)
	end)


	add_alias("job_escape", function(params) --逃跑
		local smy_escape = var["smy_escape"] or 1
		unset_timer("timer")
		unset_timer("alarm")
		if smy_escape then
			var["not_wrong_way"] = nil
			npc_in_maze_found = function()
				return nil
			end --迷宫发现npc
			npc_in_maze_action = function()
				return nil --迷宫发现npc的行为
			end
			echo("\n" .. C.c .. "<Lua>:" .. os.date("%m/%d %H:%M:%S") .. C.x .. "【战颂摩崖】：战斗中出现各种未知异常，撤退！")
			open_trigger("fight_44")
			send("set wimpy 100")
			exec("set wimpycmd halt\\alias action 战斗失败，立即撤退...")
			exec("halt;ed;halt;ed;halt;ed")

			exes("halt;ed;look;alias action 战斗失败，立即撤退...", 2)
			var["time_mark_smy"] = os.time() - 420 --失败了，提前点时间继续接颂摩崖
			close_fight()
			var["no_need_heal_qi"] = nil  --可以疗伤
			var["do_stop"] = 0
			--close_smy()
		else
			echo("\n" .. C.c .. "<Lua>:" .. os.date("%m/%d %H:%M:%S") .. C.x .. "【战颂摩崖】：强力党继续战斗！")
		end
	end)

	add_alias("job_ask", function(params) --ask
		g(3153, function()
			set_wield_weapon("smy")
			open_trigger("songmoya_1")
			send("cond")
			send("ask lu youjiao about 报效国家")
		end)
	end)

	add_alias("job_fail", function(params)
		var["time_mark_smy"] = os.time() - 420 --失败后，2分钟后就开始重新颂摩崖
		--		exec("do_quit quit")
		close_fight()
		var["no_need_heal_qi"] = nil --可以疗伤
		var["do_stop"] = 0
		exec("ed;changejob")
	end)

	add_alias("job_win", function(params)
		var["time_mark_smy"] = os.time()
		var["no_need_heal_qi"] = nil --可以疗伤
		close_fight()
		var["do_stop"] = 0
		exec('changejob')
	end)

	add_alias("after_faint", function(params) -- 【after_faint】：设置杀晕倒以后干嘛
		exec("job_fail")
	end)

	add_alias("after_faint2", function(params) -- 【after_faint2】：设置杀晕倒以后干嘛
		exec("job_fail")
	end)
	add_alias("do_job_smy", function(params)
		var["killer_name"] = "西夏武士"
		var["killer_id"] = "xixia wushi"
		var["pfm_id"] = "xixia wushi"
		set_dazuo("smy")
		exec("go_dazuo")
		echo("\n" .. C.W .. "颂摩崖任务：第 " .. C.Y .. var["smy_count"] .. C.W .. " 组。")

		local first_pfm = get_first_pfm(var["first_pfm_smy"])
		exec("kill @pfm_id;kill @pfm_id 2;" .. first_pfm)
	end)
end)

function songmoya:smy() --宋摩崖
	local smy_fangqi_skills = var["smy_fangqi_skills"] or "玄阴1剑法|灵蛇1杖法|天山1杖法"
	if var["smy_hs_use_zj"] == 1 then
		smy_hs_use_zj = true
	end
	exec('set_job_smy')
	var["flag_job"] = "smy"    --cisha
	if var["smy_pfm"] == nil then --如果未设置默认的颂摩崖pfm，那么把var["smy_pfm"]=var["pfm2"]
		var["smy_pfm"] = var["pfm2"]
	end
	add_trigger("songmoya_2", "^[ > ]*山崖北面的小路上闪出两条人影", function(params)
		var["smy_count"] = var["smy_count"] + 1
		--echo("\n"..C.W.."颂摩崖任务：第 "..C.Y..var["smy_count"]..C.W.." 组。")
		echo("\n" .. C.c .. "<Lua>:" .. os.date("%m/%d %H:%M:%S") .. C.x ..
			"【战颂摩崖】：第 " .. C.Y .. var["smy_count"] .. C.W .. " 组。")
		var["idle"] = 0
		open_fight()
		--exec("kill_job_npc")
		send("look xixia wushi 1")
		send("look xixia wushi 2")
		--set_fight("smy")
	end)
	add_trigger("songmoya_3", "^[ > ]*只听得噗通一声，眼见得你四脚朝天，跌倒在地上。", function(params)
		var["wushi1_party"] = nil
		var["wushi2_party"] = nil
		var["wushi1_skill"] = nil
		var["wushi2_skill"] = nil
		var["wushi1_name"] = nil
		var["wushi2_name"] = nil
		var["wushi1_id"] = nil
		var["wushi2_id"] = nil
		var["wushi1_faint"] = nil
		var["wushi2_faint"] = nil
		var["zj_target_1"] = 0 --战斗开始之前，标示总决未曾命中目标一
		var["zj_target_2"] = 0 --战斗开始之前，标示总决未曾命中目标二
		if var["smy_hs_use_zj"] == 1 then
			smy_hs_use_zj = true
		end
		var["idle"] = 0
		wait(40, function()
			Print("停止所有动作等待敌人！")
			send("halt")
			unset_timer("timer")
			close_heal()
			close_dazuo()
			check_busy(function()
				exec("yun_powerup none")
			end)
		end)
	end)

	--独孤九剑总决式，命中和没命中的触发
	add_trigger("songmoya_3_1", "^看起来(.*)想杀死你！", function(params)
		if var["wushi1_name"] == nil then
			var["wushi1_name"] = params[1]
			--var["wushi1_id"]=get_id(params[1])
			--echo("\n"..C.W..var["wushi1_name"]..var["wushi1_id"])
		else
			var["wushi2_name"] = params[1]
			--var["wushi2_id"]=get_id(params[1])
			--echo("\n"..C.W..var["wushi2_name"]..var["wushi2_id"])
		end
		if var["smy_hs_use_zj"] and var["smy_hs_use_zj"] == 1 then
			add_trigger("songmoya_zj_1", "^(.*)只觉得处处受制，武功中厉害之处完全无法发挥出来！", function(params)
				--var["zj_target_hit"]=trim(params[1])

				if trim(params[1]) == var["wushi1_name"] then
					var["zj_target_1"] = 1
					var["fight"] = 1
					var["pfm_id"] = var["wushi2_id"]
					send("alias pfm " .. expand(var["smy_pfm_dgjj"]))
					send("alias pfm_backup " .. expand(var["smy_pfm_dgjj"]))
					send("set wimpy 100")
					send("set wimpycmd hp " .. var["char_id"] .. "\\pfm")
					send("pfm")
					echo("\n" ..
						C.c ..
						"<Lua>:" ..
						os.date("%m/%d %H:%M:%S") ..
						C.x .. "【战颂摩崖】： " .. C.g .. var["wushi1_name"] ..
						C.W .. " 已经被总决打中，转换目标为 " .. C.g .. var["wushi2_name"] .. " ")
				end
				if trim(params[1]) == var["wushi2_name"] then
					var["zj_target_2"] = 1
					var["fight"] = 1
					var["pfm_id"] = var["wushi1_id"]
					send("alias pfm " .. expand(var["smy_pfm_dgjj"]))
					send("alias pfm_backup " .. expand(var["smy_pfm_dgjj"]))
					send("set wimpy 100")
					send("set wimpycmd hp " .. var["char_id"] .. "\\pfm")
					send("pfm")
					echo("\n" ..
						C.c ..
						"<Lua>:" ..
						os.date("%m/%d %H:%M:%S") ..
						C.x .. "【战颂摩崖】： " .. C.g .. var["wushi2_name"] ..
						C.W .. " 已经被总决打中，转换目标为 " .. C.g .. var["wushi1_name"] .. " ")
				end
				if var["zj_target_1"] == 1 and var["zj_target_2"] == 1 then
					--总决全部命中，换用杀伤性pfm
					smy_hs_use_zj = false --可以不用总决pfm了
					send("alias pfm " .. expand(var["smy_pfm"]))
					send("alias pfm_backup " .. expand(var["smy_pfm"]))
					send("set wimpy 100")
					send("set wimpycmd hp " .. var["char_id"] .. "\\pfm")
					send("pfm")
					echo("\n" .. C.c .. "<Lua>:" .. os.date("%m/%d %H:%M:%S") .. C.x .. "【战颂摩崖】：  目标全部被总决了，转换为普通杀伤性技能！ ")
				end
			end)
			add_trigger("songmoya_zj_2", "^(.*)灵机一动，身随剑走，果然你再也无法随意出招。", function(params)
				--var["zj_target_miss"]=trim(params[1])
				if trim(params[1]) == var["wushi1_name"] then
					var["zj_target_1"] = 0
					echo("\n" ..
						C.c .. "<Lua>:" ..
						os.date("%m/%d %H:%M:%S") .. C.x .. "【战颂摩崖】： " .. C.g .. var["wushi1_name"] .. C.W .. " 总决未命中！ ")
				end
				if trim(params[1]) == var["wushi2_name"] then
					var["zj_target_2"] = 0
					echo("\n" ..
						C.c .. "<Lua>:" ..
						os.date("%m/%d %H:%M:%S") .. C.x .. "【战颂摩崖】： " .. C.g .. var["wushi2_name"] .. C.W .. " 总决未命中！ ")
				end
			end)
		end
	end)

	add_trigger("songmoya_3_2", "^西夏一品堂武士\\s*(\\S+)\\((.*)\\)", function(params)
		if var["wushi1_name"] ~= nil and var["wushi1_name"] == params[1] then
			var["wushi1_id"] = string.lower(params[2])
			--echo("\n"..C.W..var["wushi1_name"].." "..var["wushi1_id"])
		end
		if var["wushi2_name"] ~= nil and var["wushi2_name"] == params[1] then
			var["wushi2_id"] = string.lower(params[2])
			--echo("\n"..C.W..var["wushi2_name"].." "..var["wushi2_id"])
		end
	end)

	add_trigger("songmoya_4", "^[ > ]*此人看上去师承(.*)，擅长使用(.*)伤敌！", function(params)
		--echo("\n"..C.W.."line3="..line[3])
		if var["wushi1_party"] == nil and string.find(line[3], var["wushi1_name"]) then
			var["wushi1_party"] = params[1]
			var["wushi1_skill"] = params[2]
			if var["wushi1_name"] and var["wushi1_id"] and var["wushi1_party"] and var["wushi1_skill"] then
				echo("\n" ..
					C.c ..
					"<Lua>:" ..
					os.date("%m/%d %H:%M:%S") ..
					C.x ..
					"【战颂摩崖】：【" ..
					C.g ..
					var["wushi1_party"] ..
					C.W ..
					"】的【" .. C.g .. var["wushi1_name"] .. C.W .. "】使用【" .. C.g .. var["wushi1_skill"] .. C.W .. "】")
				if string.find(smy_fangqi_skills, var["wushi1_skill"]) then --加入放弃技能列表
					close_fight()
					exec("halt;ed;halt;ed;halt;ed;halt;ed")
					echo("\n" ..
						C.c ..
						"<Lua>:" .. os.date("%m/%d %H:%M:%S") ..
						C.x .. "【战颂摩崖】：遇到不可力敌的【" .. C.g .. var["wushi1_skill"] .. C.W .. "】，撤退！")
				end
			end
		elseif var["wushi2_party"] == nil and string.find(line[3], var["wushi2_name"]) then
			var["wushi2_party"] = params[1]
			var["wushi2_skill"] = params[2]
			if var["wushi2_name"] and var["wushi2_id"] and var["wushi2_party"] and var["wushi2_skill"] then
				echo("\n" ..
					C.c ..
					"<Lua>:" ..
					os.date("%m/%d %H:%M:%S") ..
					C.x ..
					"【战颂摩崖】：【" ..
					C.g ..
					var["wushi2_party"] ..
					C.W ..
					"】的【" .. C.g .. var["wushi2_name"] .. C.W .. "】使用【" .. C.g .. var["wushi2_skill"] .. C.W .. "】")
				if string.find(smy_fangqi_skills, var["wushi2_skill"]) then --加入放弃技能列表
					close_fight()
					exec("halt;ed;halt;ed;halt;ed;halt;ed")
					echo("\n" ..
						C.c ..
						"<Lua>:" .. os.date("%m/%d %H:%M:%S") ..
						C.x .. "【战颂摩崖】：遇到不可力敌的【" .. C.g .. var["wushi2_skill"] .. C.W .. "】，撤退！")
				end
			end
		end
		if var["wushi1_skill"] ~= nil and var["wushi2_skill"] ~= nil then --如果两个武士的技能都判断好之后，开始根据玩家自设的技能优先级，进行pfm对象的优先排序
			local wushi1_skill = var["wushi1_skill"]
			local wushi2_skill = var["wushi2_skill"]
			local wushi1_id = var["wushi1_id"]
			local wushi2_id = var["wushi2_id"]
			local skills_pri_list = var["smy_skills_list"] or "玄阴剑法|灵蛇杖法|天山杖法|天羽奇剑|圣火令法|独孤九剑|玉女素心剑|七弦无形剑|回风拂柳剑|如意刀法"
			local wushi1_pri = string.find(skills_pri_list, wushi1_skill) or 0 --比较优先级技能列表，如果搜索到技能，则获取对应位置，否则为0
			local wushi2_pri = string.find(skills_pri_list, wushi2_skill) or 0 --比较优先级技能列表，如果搜索到技能，则获取对应位置，否则为0

			if wushi1_pri >= wushi2_pri then                          --技能优先级 武士1>=武士2,击杀顺序不用变
				echo("\n" ..
					C.c ..
					"<Lua>:" ..
					os.date("%m/%d %H:%M:%S") ..
					C.x .. "【战颂摩崖】：发现【" .. C.g .. var["wushi1_skill"] .. C.W .. "】优先对付【" .. C.g ..
					var["wushi1_name"] .. C.W .. "】")
				var["fight"] = 1
				var["pfm_id"] = var["wushi1_id"]
				var["pfm1_id"] = var["wushi2_id"]
				if smy_hs_use_zj then
					echo("\n" ..
						C.c .. "<Lua>:" .. os.date("%m/%d %H:%M:%S") ..
						C.x .. "【战颂摩崖】：总决对付【" .. C.g .. var["wushi1_name"] .. C.W .. "】")
					local sp_pfm_exist = false
					for i = 1, 10 do          --10 个技能和对应的Pfm，应该够用了吧？
						if var["smy_sp_skill" .. i] == nil then --如果未设置特殊pfm，那么强制设置为空字符串 防止比较过程中出错
							var["smy_sp_skill" .. i] = ""
						end
						if var["smy_sp_skill" .. i] == var["wushi1_skill"] then
							--echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."【战颂摩崖】：发现特殊技能列表【"..var["smy_sp_skill"..i].."】与【"..var["wushi1_skill"].."】匹配！")					
							sp_pfm_exist = true             --特殊技能有匹配，那么不能用默认值
							send("alias pfm " .. expand(var["smy_sp_pfm" .. i])) --特殊技能对应特殊pfm如果有匹配的话，那么pfm设置为对应的特殊pfm
							send("alias pfm_backup " .. expand(var["smy_sp_pfm" .. i]))
						end
					end
					if not sp_pfm_exist and var[var["smy_pfm_dgjj"]] ~= "" then --没有匹配的特殊Pfm并且设置了使用独孤九剑，那么使用独孤九剑总决式
						send("alias pfm " .. expand(var["smy_pfm_dgjj"]))
						send("alias pfm_backup " .. expand(var["smy_pfm_dgjj"]))
					end
					send("set wimpy 100")
					send("set wimpycmd hp " .. var["char_id"] .. "\\pfm")
					send("pfm")
				else
					echo("\n" ..
						C.c .. "<Lua>:" ..
						os.date("%m/%d %H:%M:%S") .. C.x .. "【战颂摩崖】：杀伤技能对付【" .. C.g .. var["wushi1_name"] .. C.W .. "】")
					local sp_pfm_exist = false
					for i = 1, 10 do          --10 个技能和对应的Pfm，应该够用了吧？
						if var["smy_sp_skill" .. i] == nil then --如果未设置特殊pfm，那么强制设置为空字符串 防止比较过程中出错
							var["smy_sp_skill" .. i] = ""
						end
						if var["smy_sp_skill" .. i] == var["wushi1_skill"] then
							--echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."【战颂摩崖】：发现特殊技能列表【"..var["smy_sp_skill"..i].."】与【"..var["wushi1_skill"].."】匹配！")					
							sp_pfm_exist = true             --特殊技能有匹配，那么不能用默认值
							send("alias pfm " .. expand(var["smy_sp_pfm" .. i])) --特殊技能对应特殊pfm如果有匹配的话，那么pfm设置为对应的特殊pfm
							send("alias pfm_backup " .. expand(var["smy_sp_pfm" .. i]))
						end
					end
					if not sp_pfm_exist then --没有匹配的特殊Pfm，那么设置为默认的var["smy_pfm"]
						send("alias pfm " .. expand(var["smy_pfm"]))
						send("alias pfm_backup " .. expand(var["smy_pfm"]))
					end
					send("set wimpy 100")
					send("set wimpycmd hp " .. var["char_id"] .. "\\pfm")
					send("pfm")
				end
			end
			if wushi1_pri < wushi2_pri then --技能优先级 武士2> 武士1,优先击杀武士2
				echo("\n" ..
					C.c ..
					"<Lua>:" ..
					os.date("%m/%d %H:%M:%S") ..
					C.x .. "【战颂摩崖】：发现【" .. C.g .. var["wushi2_skill"] .. C.W .. "】优先对付【" .. C.g ..
					var["wushi2_name"] .. C.W .. "】")
				var["fight"] = 1
				var["pfm_id"] = var["wushi2_id"]
				var["pfm1_id"] = var["wushi1_id"]
				if smy_hs_use_zj then
					--加入smy专用pfm判断
					--var["smy_sp_skill1"]="玄阴剑法"
					--var["smy_sp_pfm1"]="bei none;bei xxx;jifa parry xxx;jifa xxx xxx;jiali max;yun xxx;perform xxx.xxx @pfm_id"
					--例如：对付玄阴剑专用pfm 丐帮的perform pi
					echo("\n" ..
						C.c .. "<Lua>:" .. os.date("%m/%d %H:%M:%S") ..
						C.x .. "【战颂摩崖】：总决对付【" .. C.g .. var["wushi2_name"] .. C.W .. "】")
					local sp_pfm_exist = false
					for i = 1, 10 do          --10 个技能和对应的Pfm，应该够用了吧？
						if var["smy_sp_skill" .. i] == nil then --如果未设置特殊pfm，那么强制设置为空字符串 防止比较过程中出错
							var["smy_sp_skill" .. i] = ""
						end
						--echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."【战颂摩崖】：武士2的技能【"..var["wushi2_skill"].."】")
						if var["smy_sp_skill" .. i] == var["wushi2_skill"] then
							--echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."【战颂摩崖】：发现特殊技能列表【"..var["smy_sp_skill"..i].."】与【"..var["wushi2_skill"].."】匹配！")	
							sp_pfm_exist = true             --特殊技能有匹配，那么不能用默认值
							send("alias pfm " .. expand(var["smy_sp_pfm" .. i])) --特殊技能对应特殊pfm如果有匹配的话，那么pfm设置为对应的特殊pfm
							send("alias pfm_backup " .. expand(var["smy_sp_pfm" .. i]))
						end
					end
					if not sp_pfm_exist and var[var["smy_pfm_dgjj"]] ~= "" then --没有匹配的特殊Pfm，那么设置为默认的var["smy_pfm"]
						send("alias pfm " .. expand(var["smy_pfm_dgjj"]))
						send("alias pfm_backup " .. expand(var["smy_pfm_dgjj"]))
					end
					send("set wimpy 100")
					send("set wimpycmd hp " .. var["char_id"] .. "\\pfm")
					send("pfm")
				else
					--加入smy专用pfm判断
					--var["smy_sp_skill1"]="玄阴剑法"
					--var["smy_sp_pfm1"]="bei none;bei xxx;jifa parry xxx;jifa xxx xxx;jiali max;yun xxx;perform xxx.xxx @pfm_id"
					--例如：对付玄阴剑专用pfm 丐帮的perform pi
					echo("\n" ..
						C.c .. "<Lua>:" ..
						os.date("%m/%d %H:%M:%S") .. C.x .. "【战颂摩崖】：杀伤技能对付【" .. C.g .. var["wushi2_name"] .. C.W .. "】")
					local sp_pfm_exist = false
					for i = 1, 10 do          --10 个技能和对应的Pfm，应该够用了吧？
						if var["smy_sp_skill" .. i] == nil then --如果未设置特殊pfm，那么强制设置为空字符串 防止比较过程中出错
							var["smy_sp_skill" .. i] = ""
						end
						--echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."【战颂摩崖】：武士2的技能【"..var["wushi2_skill"].."】")
						if var["smy_sp_skill" .. i] == var["wushi2_skill"] then
							--echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."【战颂摩崖】：发现特殊技能列表【"..var["smy_sp_skill"..i].."】与【"..var["wushi2_skill"].."】匹配！")	
							sp_pfm_exist = true             --特殊技能有匹配，那么不能用默认值
							send("alias pfm " .. expand(var["smy_sp_pfm" .. i])) --特殊技能对应特殊pfm如果有匹配的话，那么pfm设置为对应的特殊pfm
							send("alias pfm_backup " .. expand(var["smy_sp_pfm" .. i]))
						end
					end
					if not sp_pfm_exist then --没有匹配的特殊Pfm，那么设置为默认的var["smy_pfm"]
						send("alias pfm " .. expand(var["smy_pfm"]))
						send("alias pfm_backup " .. expand(var["smy_pfm"]))
					end
					send("set wimpy 100")
					send("set wimpycmd hp " .. var["char_id"] .. "\\pfm")
					send("pfm")
				end
			end
		end
	end)

	add_trigger("songmoya_5_1", "^[ > ]*(\\S+)「啪」的一声倒在地上，挣扎着抽动了几下就死了。", function(params)
		var["smy_kill_count"] = var["smy_kill_count"] + 1
		--echo("\n"..C.W.."颂摩崖任务：第 "..C.Y..var["smy_count"]..C.W.." 组。")

		--exec("kill_job_npc")

		if var["wushi1_name"] and var["wushi1_name"] == params[1] then --武士1死了
			--改变pfm对象为武士2
			var["wushi1_faint"] = "武士1无威胁"
			if var["wushi2_id"] and var["wushi2_id"] ~= "" then
				var["fight"] = 1
				var["pfm_id"] = var["wushi2_id"]
				--send("alias pfm "..expand(var["pfm1"]))
				--send("alias pfm_backup "..expand(var["pfm1"]))
				--************************************************************************************************
				local sp_pfm_exist = false
				for i = 1, 10 do
					if var["smy_sp_skill" .. i] == nil then
						var["smy_sp_skill" .. i] = ""
					end
					if var["smy_sp_skill" .. i] == var["wushi2_skill"] then
						--echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."【战颂摩崖】：发现特殊技能列表【"..var["smy_sp_skill"..i].."】与【"..var["wushi2_skill"].."】匹配！")	
						sp_pfm_exist = true
						send("alias pfm " .. expand(var["smy_sp_pfm" .. i]))
						send("alias pfm_backup " .. expand(var["smy_sp_pfm" .. i]))
					end
				end
				if not sp_pfm_exist then
					send("alias pfm " .. expand(var["smy_pfm"]))
					send("alias pfm_backup " .. expand(var["smy_pfm"]))
				end
				--************************************************************************************************
				send("set wimpy 100")
				send("set wimpycmd hp " .. var["char_id"] .. "\\pfm")
				wait(0.5, function() --等待0.2s以后再fight
					exec("fight " .. var["wushi2_id"])
				end)
			end
		end
		if var["wushi2_name"] and var["wushi2_name"] == params[1] then --武士2死了
			var["wushi2_faint"] = "武士2无威胁"
			--改变pfm对象为武士1
			if var["wushi1_id"] and var["wushi1_id"] ~= "" then
				var["fight"] = 1
				var["pfm_id"] = var["wushi1_id"]
				--send("alias pfm "..expand(var["pfm1"]))
				--send("alias pfm_backup "..expand(var["pfm1"]))
				--************************************************************************************************
				local sp_pfm_exist = false
				for i = 1, 10 do
					if var["smy_sp_skill" .. i] == nil then
						var["smy_sp_skill" .. i] = ""
					end
					if var["smy_sp_skill" .. i] == var["wushi1_skill"] then
						--echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."【战颂摩崖】：发现特殊技能列表【"..var["smy_sp_skill"..i].."】与【"..var["wushi1_skill"].."】匹配！")	
						sp_pfm_exist = true
						send("alias pfm " .. expand(var["smy_sp_pfm" .. i]))
						send("alias pfm_backup " .. expand(var["smy_sp_pfm" .. i]))
					end
				end
				if not sp_pfm_exist then
					send("alias pfm " .. expand(var["smy_pfm"]))
					send("alias pfm_backup " .. expand(var["smy_pfm"]))
				end
				--************************************************************************************************
				send("set wimpy 100")
				send("set wimpycmd hp " .. var["char_id"] .. "\\pfm")
				wait(0.5, function() --等待0.2s以后再fight
					exec("fight " .. var["wushi1_id"])
				end)
			end
		end
	end)

	add_trigger("songmoya_5_2", "^[ > ]*(\\S+)神志迷糊，脚下一个不稳，倒在地上昏了过去。", function(params)
		if var["wushi1_name"] and var["wushi1_name"] == params[1] then --武士1昏过去
			var["wushi1_faint"] = "武士1无威胁"
			--改变pfm对象为武士2
			exec("kill " .. var["wushi1_id"])
			if var["wushi2_id"] and var["wushi2_id"] ~= "" then
				var["fight"] = 1
				var["pfm_id"] = var["wushi2_id"]
				--send("alias pfm "..expand(var["pfm1"]))
				--send("alias pfm_backup "..expand(var["pfm1"]))
				--************************************************************************************************
				local sp_pfm_exist = false
				for i = 1, 10 do
					if var["smy_sp_skill" .. i] == nil then
						var["smy_sp_skill" .. i] = ""
					end
					if var["smy_sp_skill" .. i] == var["wushi2_skill"] then
						--echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."【战颂摩崖】：发现特殊技能列表【"..var["smy_sp_skill"..i].."】与【"..var["wushi2_skill"].."】匹配！")	
						sp_pfm_exist = true
						send("alias pfm " .. expand(var["smy_sp_pfm" .. i]))
						send("alias pfm_backup " .. expand(var["smy_sp_pfm" .. i]))
					end
				end
				if not sp_pfm_exist then
					send("alias pfm " .. expand(var["smy_pfm"]))
					send("alias pfm_backup " .. expand(var["smy_pfm"]))
				end
				--************************************************************************************************
				send("set wimpy 100")
				send("set wimpycmd hp " .. var["char_id"] .. "\\pfm")
				wait(0.5, function() --等待0.2s以后再fight
					exec("fight " .. var["wushi2_id"])
				end)
			end
		end
		if var["wushi2_name"] and var["wushi2_name"] == params[1] then --武士2昏过去
			var["wushi2_faint"] = "武士2无威胁"
			--改变pfm对象为武士1
			exec("kill " .. var["wushi2_id"])
			if var["wushi1_id"] and var["wushi1_id"] ~= "" then
				var["fight"] = 1
				var["pfm_id"] = var["wushi1_id"]
				--send("alias pfm "..expand(var["pfm1"]))
				--send("alias pfm_backup "..expand(var["pfm1"]))
				--************************************************************************************************
				local sp_pfm_exist = false
				for i = 1, 10 do
					if var["smy_sp_skill" .. i] == nil then
						var["smy_sp_skill" .. i] = ""
					end
					if var["smy_sp_skill" .. i] == var["wushi1_skill"] then
						--echo("\n"..C.c.."<Lua>:"..os.date("%m/%d %H:%M:%S")..C.x.."【战颂摩崖】：发现特殊技能列表【"..var["smy_sp_skill"..i].."】与【"..var["wushi1_skill"].."】匹配！")	
						sp_pfm_exist = true
						send("alias pfm " .. expand(var["smy_sp_pfm" .. i]))
						send("alias pfm_backup " .. expand(var["smy_sp_pfm" .. i]))
					end
				end
				if not sp_pfm_exist then
					send("alias pfm " .. expand(var["smy_pfm"]))
					send("alias pfm_backup " .. expand(var["smy_pfm"]))
				end
				--************************************************************************************************
				send("set wimpy 100")
				send("set wimpycmd hp " .. var["char_id"] .. "\\pfm")
				wait(0.5, function() --等待0.2s以后再fight
					exec("fight " .. var["wushi1_id"])
				end)
			end
		end
	end)
	add_trigger("songmoya_6", "^[ > ]*(看清楚一点，那并不是|你想攻击谁？)", function(params)
		del_timer("input")
		var["do_stop"] = 0
		if var["smy_kill_count"] >= var["smy"] * 2 then --做够指定组数，下山
			fight_end()
			close_fight()
			--exec("get gold;get silver;get silver from corpse;get gold from corpse;get silver from corpse 2;get gold from corpse 2;get silver from corpse 3;get gold from corpse 3")
			wait(4, function()
				exec("ed")
				var["smy_count"] = 0
				var["smy_kill_count"] = 0
				var["smy_over"] = true
			end)
		else
			wait(4, function()
				--exec("get gold;get silver;get silver from corpse;get gold from corpse;get silver from corpse 2;get gold from corpse 2;get silver from corpse 3;get gold from corpse 3")
				--set_dazuo("smy")
				exec("flop;flop;yun jing;yun jingli;yun qi;check_poison check_heal do_job_smy")
			end)
		end
	end)

	add_trigger("songmoya_7", "^[ > ]*你速度太慢，西夏武士已过颂摩崖，任务失败。", function(params)
		fight_end()
		close_fight()
		close_smy()
		set_dazuo("changejob")
		check_busy(function()
			exec("job_fail")
		end)
	end)
	add_trigger("songmoya_8", "^[ > ]*恭喜你！你成功的完成了颂摩崖任务！你被奖励了：", function(params)
		set_end_time("颂摩崖")
		fight_end()
		close_fight()
		close_smy()
		set_dazuo("changejob")
		var["smy_over"] = true
		check_busy(function()
			exec("job_win")
			--exec("get gold from corpse;get gold from corpse 2;get silver from corpse;get silver from corpse 2;job_win")
		end)
	end)
	add_trigger("songmoya_9", "^[ > ]*一阵呼啸的山风刮过，山两侧的峭壁显得格外阴森。", function(params)
		var["idle"] = 0
	end)
	--你只觉得头昏脑胀，眼前一黑，接着什么也不知道了……   失败，被打晕了
	add_trigger("songmoya_10", "^[ > ]*你只觉得头昏脑胀，眼前一黑，接着什么也不知道了……", function(params)
		echo("\n" .. C.c .. "<Lua>:" .. os.date("%m/%d %H:%M:%S") .. C.x .. "【战颂摩崖】：" .. C.R .. "很不幸，被npc打懵逼了，你眼前一黑，失败了！")
		var["time_mark_smy"] = os.time() - 420 --失败了,提前点接颂摩崖任务
		close_fight()
		var["no_need_heal_qi"] = nil     --可以疗伤
		var["do_stop"] = 0
		close_smy()
	end)

	add_alias("do_smy", function(params)
		var["smy_over"] = false
		var["job_zone"] = "星宿海"
		var["job_room"] = "颂摩崖"
		var["log_zone"] = "星宿海"
		var["log_room"] = "颂摩崖"
		var["smy_count"] = 0
		var["smy_kill_count"] = 0
		del_timer("wait")
		g(1961, function()
			set_start_time("颂摩崖")
			var["killer_name"] = "西夏武士"
			var["killer_id"] = "xixia wushi"
			var["pfm_id"] = "xixia wushi"
			set_wield_weapon("smy")
			--		set_fight("smy")
			send("alias pfm " .. expand(var["smy_pfm"]))
			send("alias pfm_backup " .. expand(var["smy_pfm"]))
			send("set wimpy 100")
			send("set wimpycmd hp " .. var["char_id"] .. "\\pfm")
			exec("yun_powerup none")
		end)
	end)
	exec("do_smy")
end

function close_smy() --关闭触发
	delete_triggers("songmoya", 2, 6)
	--del_trigger("songmoya_7") --永远保留颂摩崖失败的触发提示
	delete_triggers("songmoya", 8, 10)
	delete_triggers("songmoya_3", 1, 2)
	delete_triggers("songmoya_5", 1, 2)
	delete_triggers("songmoya_zj", 1, 2)
end
