--heal.lua

add_alias("do_healqi",function(params)
--
add_trigger("heal_1","^[ > ]*(?:你并没有受伤！|没受伤疗什么伤？)",function(params)
--var["idle"]=0
	var["no_need_heal_qi"]=1
	close_heal()
	exec("after_heal")
end)
add_trigger("heal_2","^[ > ]*(?:你呼出一口气站了起来，可惜伤势还没有完全恢复。|你的真气不够。)",function(params)
--var["idle"]=0
	del_timer("timer")
	set_dazuo("health")
	wait(3,function()
	send("yun qi")
	exec("go_dazuo")
	end)
end)
add_trigger("heal_3","^[ > ]*你(?:连催四道冷泉内劲|运起寒冰真气，开始缓缓运气疗伤。|收蹑心神，屏息静气，缓缓开始运功疗伤。|双手合什，盘膝而坐|神情肃然，双目虚闭|全身放松，半蹲在地上|盘膝坐下，依照经中所示|盘膝坐下，蓦然想起|盘膝坐下，开始运功疗伤。|盘膝而坐，双手十指张开|凝神静气，内息随悠扬箫声)",function(params)
--你连催四道冷泉内劲游走受损经络开始运功疗伤！
	var["idle"]=0
	del_timer("timer")
	del_timer("wait")
	del_timer("idle")
end)
add_trigger("heal_4","^[ > ]*你还没有选择你要使用的内功。",function(params)
	check_busy2(function()
		send("jifa force "..var["jifa force"])
		exec("yun_heal")
	end)
end)
add_trigger("heal_5","^[ > ]*你已经受伤过重，经受不起真气震荡！",function(params)
	local hurtqi=var["hurtqi"] or 100
	local myheal=var["myheal"] or 50
	local dahuandan=item["dahuan dan"] or 0
	local usedahuandan=var["usedahuandan"] or 0
	local chantuiyao=item["chantui yao"] or 0
	local gold=item["gold"] or 0
	if dahuandan>0 and (usedahuandan==1 or usedahuandan==3) then
		send("eat dahuan dan")
		close_heal()
		exec("after_heal")
	elseif chantuiyao>0 then
		set_dazuo("heal")
		check_place(function()
			send("fu chantui yao")
			send("yun jing")
			send("yun qi")
			send("hp")
			send("i")
			exec("yun_heal")
		end)
	elseif gold<4 then
		wait(3,function()
			exec("do_qu 5 gold")
		end)
	else
		wait(3,function()
			exec("do_buy chantui yao")
		end)
	end
end)
add_trigger("heal_6","^[ > ]*(\\S+)并没有受伤！",function(params)
	local player=params[1]
	if string.find(player,var["char_name"]) then
	close_heal()
	exec("after_heal")	
	end
end)
add_trigger("heal_7","^[ > ]*(?:你|良久，你|九阳神功的威力，这时方才显现出来，在你|过了良久，琴音顿止，你|就一眨眼功夫，你)(?:双眼缓缓睁开|运功完毕，站起身来，看上去气色饱满，精神抖擞。|长吸一口气，精神抖擞的站了起来。|体内又运走数转|脸上流光浮现|脸色渐渐变得舒缓|将草木精华与内息|缓缓站起，只觉全身说不出的舒服畅快|感觉通过自己的内息运行|“哇！”的大叫一声)",function(params)
--就一眨眼功夫，你双眼缓缓睁开，长长地吐出了一口浊气，哪里还有半点受伤的样子！

	var["idle"]=0
	close_heal()
	exec("after_heal")
end)

--

local master=var["master"]
local party=var["party"] or "none"
local skills_jifa=var["skills_jifaid"] or {["force"]="none"}
local skills_level=var["skills_level"] or {}
local force=skills_jifa["force"] or "none"
if string.find(force,"kurong") and skills_level["force"] and skills_level["force"]>150 and skills_level["medicine"] and skills_level["medicine"]>119 and skills_level["yiyang-zhi"] and skills_level["yiyang-zhi"]>150 then
	add_alias("yun_heal",function(params)
		send("unset heal")
		set_timer("timer",1,function()
			exec("bei none;jifa finger yiyang-zhi;bei finger;perform finger.liao;yun liao")
		end)
	end)
	var["yun_heal"]="yyz"
elseif string.find(force,"longxiang") and skills_level["medicine"] and skills_level["medicine"]>80 and skills_level["longxiang-boruo"] and skills_level["longxiang-boruo"]>100 then
	add_alias("yun_heal",function(params)
		send("unset heal")
		send("yun juxue")
	end)
	var["yun_heal"]="longxiang"
else
	add_alias("yun_heal",function(params)
		send("unset heal")
		send("yun heal")
	end)
	var["yun_heal"]="yunheal"
end


	add_alias("fail_xue_heal",function(params)
		exec("look;i;fail_xue_heal_qi") --薛慕华liaoqi失败
	end)

local hurtqi=var["hurtqi"] or 100
local healqi=params[-1]
local shedu=var["shedu"] or 0
--healqi=self 就是强制自己疗伤啦

if healqi and string.find(healqi,"prepare_sx2") then -- sx2 疗伤
	add_alias("after_heal",function(params)
		exec("do_prepare_sx2")
	end)
	if var["no_need_heal_qi"]~=nil then
		exec("after_heal")
	else
		if hurtqi>85 then
			set_dazuo("heal")
			check_place(function()
				exec("yun jing;yun qi;hp;yun_heal")
			end)
		elseif var["yun_heal"]=="yyz" and hurtqi>35 then --yyz
			set_dazuo("heal")
				check_place(function()
					exec("yun jing;yun qi;hp;yun_heal")
				end)
		elseif var["yun_heal"]=="longxiang" and hurtqi>60 then --longxiang
			set_dazuo("heal")
			check_place(function()
				exec("yun jing;yun qi;hp;yun_heal")
			end)
		else --放弃送信2
			var["sx2_fangqi"]=1
			wait(1,function()
				exec("do_job_sx")
			end)
		end
	end
elseif healqi and string.find(healqi,"gm") then -- gm liao bed
	add_alias("after_faint",function()
		exec("changejob")
	end)
	g(1595,function()
		exec("tiao gou")
		g(3396,function()
			exec("liao bed")
			check_busy(function()
				var["time_mark_liaobed"]=os.time()
				exec("changejob")			
			end)
		end)
	end)
	
elseif healqi and string.find(healqi,"prepare_tdh") then -- tdh 疗伤
var["do_stop"]=0
	add_alias("after_heal",function(params)
--		exec("do_prepare_tdh") --准备tdh
		set_dazuo("tdh_dazuo")
		exec("go_dazuo")
	end)
			local dahuandan1=0
			local dahuandan2=0
			local chuanbeiwan=0
			local usedrug=var["usedahuandan"] or 0--用药
			local usechuanbei=var["chuanbeiwan"] or 0
			local usedahuandan=var["dahuandan"] or 0
			local tongbao=var["tongbao"] or 0 --通宝总数
			if not null(item) then
				dahuandan1=item["da huandan"] or 0
				dahuandan2=item["dahuan dan"] or 0
				chuanbeiwan=item["chuanbei wan"] or 0
			end
	if 1==1 and var["no_need_heal_qi"]==nil then
		exec("after_heal")
	else
		if hurtqi>85 then
			set_dazuo("tdh_heal")
			check_place(function()
				exec("yun jing;yun qi;hp;yun_heal")
			end)
		elseif var["yun_heal"]=="yyz" and hurtqi>35 then --yyz
			set_dazuo("tdh_heal")
				check_place(function()
					exec("yun jing;yun qi;hp;yun_heal")
				end)
		elseif var["yun_heal"]=="longxiang" and hurtqi>60 then --longxiang
			set_dazuo("tdh_heal")
			check_place(function()
				exec("yun jing;yun qi;hp;yun_heal")
			end)
		elseif usedrug>2 and usedahuandan>0 and (dahuandan1>0 or dahuandan2>0) then --大还丹
			exec("eat dan;yun jing;yun qi;hp;yun_heal")
		
		elseif hurtqi>30 then
			set_dazuo("tdh_heal")
			check_place(function()
				exec("yun jing;yun qi;hp;yun_heal")
			end)
		else --放弃tdh quit吧
			close_fight()
			close_tdh()
			var["do_stop"]=0
			exec("do_quit quit")
		end
	end
elseif healqi and (string.find(healqi,"do_job_huoshao") or string.find(healqi,"do_job_cisha") or string.find(healqi,"do_job_swxy") or string.find(healqi,"do_job_smy") or string.find(healqi,"do_job_slhs") or string.find(healqi,"nextquest")) then -- 刺杀 或火烧 或其他任务
	
		if string.find(healqi,"do_job_cisha") then
			add_alias("after_heal",function(params)
				set_dazuo("cisha")
				exec("yun jing;yun jingli;yun qi;go_dazuo")
			end)
		elseif string.find(healqi,"do_job_smy") then
			add_alias("after_heal",function(params)
				set_dazuo("smy")
				exec("yun jing;yun jingli;yun qi;go_dazuo")
			end)
		elseif string.find(healqi,"do_job_swxy") then
			add_alias("after_heal",function(params)
				set_dazuo("swxy")
				exec("yun jing;yun jingli;yun qi;go_dazuo")
			end)
		elseif string.find(healqi,"do_job_slhs") then
			add_alias("after_heal",function(params)
				set_dazuo("slhs")
				exec("yun jing;yun jingli;yun qi;go_dazuo")
			end)
		elseif string.find(healqi,"nextquest") then
			add_alias("after_heal",function(params)
				set_dazuo("nextquest")
				exec("yun jing;yun jingli;yun qi;go_dazuo")
			end)
		else--火烧
			set_dazuo("huoshao")
				exec("yun jing;yun jingli;yun qi;go_dazuo")
		end
			
	if var["no_need_heal_qi"]~=nil then
		exec("after_heal")
	else
		if hurtqi>85 then
			set_dazuo("heal")
			check_place(function()
				exec("yun jing;yun qi;hp;yun_heal")
			end)
		elseif var["yun_heal"]=="yyz" and hurtqi>35 then --yyz
			set_dazuo("heal")
				check_place(function()
					exec("yun jing;yun qi;hp;yun_heal")
				end)
		elseif var["yun_heal"]=="longxiang" and hurtqi>60 then --longxiang
			set_dazuo("heal")
			check_place(function()
				exec("yun jing;yun qi;hp;yun_heal")
			end)
		else
			set_dazuo("heal")
			check_place(function()
				exec("yun jing;yun qi;hp;yun_heal")
			end)
		end
	end
else--准备job疗伤
	add_alias("after_heal",function(params)
		exec("changejob")
	end)
	if var["no_need_heal_qi"]~=nil then
		exec("after_heal")
	else
		if hurtqi>95 then --95% 自己疗伤
			set_dazuo("heal")
			check_place(function()
				exec("yun jing;yun qi;hp;yun_heal")
			end)
		elseif string.find(master,"张无忌") and shedu<5 then --mingjiao
			exec("do_healjing mj")
		elseif string.find(party,"逍遥派") and 1==1 then --暂时xy
			exec("do_healjing xy")
--		elseif string.find(party,"古墓派") and var["time_mark_liaobed"]==nil and 1==1 then --暂时关闭gm liao bed
--			exec("do_healqi gm")
		elseif var["yun_heal"]=="yyz" and hurtqi>35 then --yyz
			set_dazuo("heal")
			check_place(function()
				exec("yun jing;yun qi;hp;yun_heal")
			end)
		elseif var["yun_heal"]=="longxiang" and hurtqi>60 then --longxiang
			set_dazuo("heal")
			check_place(function()
				exec("yun jing;yun qi;hp;yun_heal")
			end)

			
		else

				exec("do_healjing xue")
		end
	end
end
--


end)



add_alias("do_healjing",function(params)
local healjing=params[-1]
local master=var["master"]
local party=var["party"]
if healjing=="prepare" then
	add_alias("after_heal",function(params)
		exec("changejob")
	end)
	add_alias("fail_xue_heal",function(params)
		exec("look;i;fail_xue_heal_jing") --薛慕华liaojing失败
	end)
	if string.find(master,"张无忌") then
		exec("do_healjing mj")
	elseif string.find(party,"逍遥派") then
		exec("do_healjing xy")
	else
		exec("do_healjing xue")
	end
elseif healjing=="xy" then --XY
	g(3656,function()
		exec("ask xue muhua about 疗伤")
		check_busy2(function()
			exec("changejob")
		end)
	end)
elseif healjing=="mj" then --胡青牛
	add_trigger("mjheal_1","^[ > ]*胡青牛狠狠地敲着你的头，你数着脑袋上的大包，吓得昏了过去。",function (params)
		unset_timer("wait")
		del_trigger("mjheal_1")
		del_trigger("mjheal_2")
				del_trigger("mjheal_3")
				del_trigger("mjheal_4")
				check_busy(function(params)
		exec("out;after_heal")
		end)
	end)
	add_trigger("mjheal_2","^[ > ]*胡青牛连点你身上的穴道，再施金针。你感觉舒服多了。",function (params)
		unset_timer("wait")
--		del_trigger("mjheal_1")
--		del_trigger("mjheal_2")
				del_trigger("mjheal_3")
				send("out")
			wait(25,function()
				check_busy(function()
					del_trigger("mjheal_1")
		del_trigger("mjheal_2")
		del_trigger("mjheal_3")
				del_trigger("mjheal_4")
				exec("out;changejob")
			end)
		end)
	end)
	add_trigger("mjheal_3","^[ > ]*胡青牛说道：「你中的毒我无能为力，还是另请高明吧。」",function (params)
		unset_timer("wait")
		del_trigger("mjheal_1")
		del_trigger("mjheal_2")
		del_trigger("mjheal_3")
				del_trigger("mjheal_4")
		check_busy(function()
			wait(3,function()	
	exec("out;check_poison changejob")
			end)
		end)
	end)
	--胡青牛说道：「你中的毒我无能为力，还是另请高明吧。」
--> 过了一会儿，你觉得伤势完全回复了！
	add_trigger("mjheal_4","^[ > ]*过了一会儿，你觉得伤势完全回复了！",function (params)
		unset_timer("wait")
		del_trigger("mjheal_1")
		del_trigger("mjheal_2")
		del_trigger("mjheal_3")
		del_trigger("mjheal_4")
		
		check_busy(function()

	exec("out;changejob")

		end)
	end)

	function after_gps() exec("goto 3453") end
	function after_goto() 
			wait(10,function()
				exec("out;changejob")
			end)
		send("ask hu qingniu about 疗伤") 
	end
	exec("gps")

elseif healjing=="xue" then	--薛慕华
	var["teachxuenum"]=1
	var["teachxuetimes"]=0
	var["teachxueover"]=0
	add_trigger("xueheal_1","^[ > ]*(?:薛慕华\\(Xue muhua\\)告诉你：你的功夫太差。|你的这个技能太差了，薛神医可没兴趣。|薛慕华说道：「你的功夫太差了。」)",function (params)
		del_timer("wait")
		del_timer("input")
		local teachskills=var["skills"] or {"force"}
		local num=var["teachxuenum"]
		local maxnum=var["teachxuemaxnum"]
		if num<maxnum then
			num=num+1
			var["teachxuenum"]=num
			exes("teach xue "..teachskills[var["teachxuenum"]],2)
			wait(2,function()
				send("alias action Teach xue muhua finish?")
		--	
		--		send("compare xue muhua")
			end)			
		else
--		del_timer("wait")
		del_trigger("xueheal_1")
		del_trigger("xueheal_2")
		del_trigger("xueheal_3")
		del_trigger("xueheal_4")
		del_trigger("xueheal_5")
		del_trigger("xueheal_6")
		del_trigger("xueheal_7")
		var["teachxueover"]=0
		exec("fail_xue_heal")
		end
			
	end)
	add_trigger("xueheal_2","^[ > ]*你向薛慕华仔细地解说。|薛神医的这个技能已经不能再进步了。",function (params)
		del_timer("wait")
		del_timer("input")
		local teachskills=var["skills"] or {"force"}
		local times=var["teachxuetimes"]
		times=times+1
		var["teachxuetimes"]=times
		if times>4 then
			send("ask xue muhua about 疗伤")
			wait(15,function()
				del_trigger("xueheal_1")
				del_trigger("xueheal_2")
				del_trigger("xueheal_3")
				del_trigger("xueheal_4")
				del_trigger("xueheal_6")
				del_trigger("xueheal_7")
				local teachxueover=var["teachxueover"] or 0
				if teachxueover==0 then
					exec("fail_xue_heal")
				else
					exec("after_heal")
				end
			end)
		else
			exes("teach xue "..teachskills[var["teachxuenum"]],2)
			wait(2,function()

				send("alias action Teach xue muhua finish?")
				--var["teachxuetimes"]>4 finish
		--		send("compare xue muhua")
			end)
		end
	end)

	add_trigger("xueheal_3","^[ > ]*xue muhua 不在这里",function (params)
		del_timer("wait")
		del_trigger("xueheal_finish")
		del_trigger("xueheal_1")
		del_trigger("xueheal_2")
		del_trigger("xueheal_3")
		del_trigger("xueheal_4")
		del_trigger("xueheal_5")
		del_trigger("xueheal_6")
		del_trigger("xueheal_7")
		var["teachxueover"]=0
		exec("fail_xue_heal")
	end)
	
	add_trigger("xueheal_4","^[ > ]*(?:薛慕华狠狠地敲着你的头，你数着脑袋上的大包，吓得昏了过去|一柱香的工夫过去了，你觉得伤势已经基本痊愈了。)",function (params)
		del_timer("wait")
		del_trigger("xueheal_finish")
		del_trigger("xueheal_1")
		del_trigger("xueheal_2")
		del_trigger("xueheal_3")
		del_trigger("xueheal_4")
		del_trigger("xueheal_5")
		del_trigger("xueheal_6")
		del_trigger("xueheal_7")
		var["teachxueover"]=1
		exec("after_heal")
	end)
	
	add_trigger("xueheal_5","^[ > ]*(?:但是很显然的，薛慕华现在的状况没有办法给你任何答覆。|薛慕华「啪」的一声倒在地上，挣扎着抽动了几下就死了。)",function (params)
		del_timer("wait")
		del_timer("input")
		del_trigger("xueheal_finish")
		var["teachxueover"]=0
		del_trigger("xueheal_1")
		del_trigger("xueheal_2")
		del_trigger("xueheal_3")
		del_trigger("xueheal_4")
		del_trigger("xueheal_5")
		del_trigger("xueheal_6")
		del_trigger("xueheal_7")
		exec("fail_xue_heal")
	end)
	add_trigger("xueheal_finish","^[ > ]*你把\\s+\"action\"\\s+设定为\\s+\"Teach xue muhua finish",function (params)
		del_trigger("xueheal_finish")
					if var["teachxuetimes"] and var["teachxuetimes"]>4 then --成功了
							send("ask xue muhua about 疗伤")
							wait(15,function()
								del_trigger("xueheal_1")
								del_trigger("xueheal_2")
								del_trigger("xueheal_3")
								del_trigger("xueheal_4")
								del_trigger("xueheal_6")
								del_trigger("xueheal_7")
								local teachxueover=var["teachxueover"] or 0
								if teachxueover==0 then
									exec("fail_xue_heal")
								else
									exec("after_heal")
								end
							end)					
					else --没成功
							del_timer("wait")
							del_trigger("xueheal_finish")
							del_trigger("xueheal_1")
							del_trigger("xueheal_2")
							del_trigger("xueheal_3")
							del_trigger("xueheal_4")
							del_trigger("xueheal_5")
							del_trigger("xueheal_6")
							del_trigger("xueheal_7")
							var["teachxueover"]=0
							exec("fail_xue_heal")
					end
	end)
	
	--你想利用 BUG 么？
--你轻轻地拍了拍薛慕华的头。
	add_trigger("xueheal_6","^[ > ]*你轻轻地拍了拍薛慕华的头。",function (params)
		var["xuemuhua_here"]=1
	end)
	add_trigger("xueheal_7","^[ > ]*你把\\s+\"action\"\\s+设定为\\s+\"薛慕华还在不",function (params)
		del_timer("input")
		del_trigger("xueheal_6")
		del_trigger("xueheal_7")
		if var["xuemuhua_here"] and var["xuemuhua_here"]==1 then
			var["xuemuhua_here"]=nil
			local teachskills=var["skills"] or {"force"}
			var["teachxuemaxnum"]=table.maxn(teachskills)
			local teachskills=var["skills"] or {"force"}
			exes("teach xue "..teachskills[var["teachxuenum"]],2)
			wait(2,function()
				send("compare xue muhua")
			end)
		else
			del_timer("wait")
			del_trigger("xueheal_finish")
			del_trigger("xueheal_1")
			del_trigger("xueheal_2")
			del_trigger("xueheal_3")
			del_trigger("xueheal_4")
			del_trigger("xueheal_5")
			del_trigger("xueheal_6")
			del_trigger("xueheal_7")
			var["teachxueover"]=0
			exec("fail_xue_heal")
		end
	end)
	g(1567,function()
		var["xuemuhua_here"]=nil
		exes("get shoes;get xue sui;pat xue;alias action 薛慕华还在不...",3)
	end)

else

	exec("fail_xue_heal_jing")
	
end


end)

add_alias("fail_xue_heal_jing",function(params)
local dahuandan=item["dahuan dan"] or 0
local dahuandan2=item["da huandan"] or 0
local usedahuandan=var["usedahuandan"] or 0
local huoxuedan=item["huoxue dan"] or 0
local gold=item["gold"] or 0
if (dahuandan>0 or dahuandan2>0) and (usedahuandan==1 or usedahuandan==3 or usedahuandan==4) then
	send("eat dahuan dan")
	exec("after_heal")
elseif huoxuedan>0 then
	send("fu huoxue dan")
	send("yun jing")
	send("yun qi")
	wait(3,function()
		exec("after_heal")
	end)
elseif gold<4 then
	wait(3,function()
		exec("do_qu 5 gold")
	end)
else
	wait(3,function()
		exec("do_buy huoxue dan")
	end)
end

end)

add_alias("fail_xue_heal_qi",function(params)
local hurtqi=var["hurtqi"] or 100
local myheal=var["myheal"] or 50
local dahuandan=item["dahuan dan"] or 0
local dahuandan2=item["da huandan"] or 0
local usedahuandan=var["usedahuandan"] or 0
local chantuiyao=item["chantui yao"] or 0
local gold=item["gold"] or 0


local myexp=var["exp"] or 150000
local r=0
if myexp>300000000 then
	r=math.random(5)--300m 1/5几率
elseif myexp>100000000 then
	r=math.random(15)--100m 1/15几率
elseif myexp>20000000 then
	r=math.random(30) --20m 1/30 几率
elseif myexp>10000000 then
	r=math.random(150) --10m 1/150 几率
end
	
local get_npc=check_room_obj("薛慕华")
if var["yun_heal"]=="yyz" or (var["yun_heal"]=="longxiang" and hurtqi>35) or (hurtqi>math.max(myheal,32)) then
	exec("unwield_weapon;get xue sui;s")
	set_dazuo("heal")
	check_place(function()
		exec("drop shoes;yun jing;yun qi;i;yun_heal")
	end)
elseif (dahuandan>0 or dahuandan2>0) and (usedahuandan==1 or usedahuandan==3 or usedahuandan==4) then
	send("eat dahuan dan")
	close_heal()
	exec("after_heal")
elseif hurtqi>50 and r==2 and get_npc then --杀薛慕华,气血足，随机
	add_trigger("xueheal_20","^[ > ]*薛慕华「啪」的一声倒在地上，挣扎着抽动了几下就死了。",function (params)
		exec("look;alias action 薛慕华还在么...")
	end)
	add_trigger("xueheal_21","^[ > ]*你把\\s+\"action\"\\s+设定为\\s+\"薛慕华还在么",function (params)
		local get_npc=check_room_obj("薛慕华")
		if get_npc==false then
			exes("look;alias action 薛慕华还在么...",120)
		else
		del_timer("input")
		del_trigger("xueheal_20")
		del_trigger("xueheal_21")
		exec("changejob")
		end
	end)
	send("wield_weapon")
	send("kill xue muhua")
	exes("look;alias action 薛慕华还在么...",120)
elseif chantuiyao>0 then
	set_dazuo("heal")
	check_place(function()
		send("s")
		send("drop shoes")
		send("fu chantui yao")
		send("yun jing")
		send("yun qi")
		send("hp")
		send("i")
		exec("yun_heal")
	end)
elseif gold<4 then
	wait(3,function()
		exec("do_qu 5 gold")
	end)
else
	wait(3,function()
		exec("do_buy chantui yao")
	end)
end


end)

function close_heal()
	del_trigger("heal_1")
	del_trigger("heal_2")
	del_trigger("heal_3")
	del_trigger("heal_4")
	del_trigger("heal_5")
	del_trigger("heal_6")
	del_trigger("heal_7")
end
Print("--- 加载模块: 疗伤 ---")